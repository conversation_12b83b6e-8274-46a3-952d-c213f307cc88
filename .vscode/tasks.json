{"tasks": [{"type": "cppbuild", "label": "C/C++: gcc build active file", "command": "/usr/bin/gcc", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}, {"type": "cppbuild", "label": "C/C++: g++ build active file", "command": "/usr/bin/g++", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "Task generated by <PERSON>bugger."}, {"type": "cppbuild", "label": "C/C++: g++-13 build active file", "command": "/usr/bin/g++-13", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "Task generated by <PERSON>bugger."}], "version": "2.0.0"}